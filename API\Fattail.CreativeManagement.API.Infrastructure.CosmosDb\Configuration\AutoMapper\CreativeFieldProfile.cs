using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using CreativeFieldEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using SelectOptionEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectOption;
using CreativeFieldDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeField;
using SelectOptionDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.Settings.SelectOption;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using CreativeFieldSettingsEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeFieldSettings;
using DefaultCreativeFieldSettingsEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.DefaultCreativeFieldSettings;
using SelectCreativeFieldSettingsEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectCreativeFieldSettings;
using CreativeFieldSettingsDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeFieldSettings;
using DefaultCreativeFieldSettingsDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.Settings.DefaultCreativeFieldSettings;
using SelectCreativeFieldSettingsDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.Settings.SelectCreativeFieldSettings;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper
{
    internal class CreativeFieldProfile : Profile
    {
        public CreativeFieldProfile ()
        {
            CreateMap<CreativeFieldSettingsEntity, CreativeFieldSettingsDomain>()
                .IncludeAllDerived();
            CreateMap<CreativeFieldSettingsDomain, CreativeFieldSettingsEntity>()
                .IncludeAllDerived();

            CreateMap<DefaultCreativeFieldSettingsEntity, DefaultCreativeFieldSettingsDomain>()
                .ReverseMap();
            CreateMap<SelectCreativeFieldSettingsEntity, SelectCreativeFieldSettingsDomain>()
                .ReverseMap();

            CreateMap<CreativeFieldEntity, CreativeFieldDomain>()
                .ForMember(dest => dest.Settings, opt => opt.MapFrom(src => src.Settings));

            CreateMap<CreativeFieldDomain, CreativeFieldEntity>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.ToString()))
                .ForMember(dest => dest.OrgId, opt => opt.Ignore())
                .ForMember(dest => dest.LastAction, opt => opt.Ignore())
                .ForMember(dest => dest.Options, opt => opt.MapFrom(src =>
                    src.Settings.GetType() == typeof(SelectCreativeFieldSettingsDomain)
                        ? ((SelectCreativeFieldSettingsDomain)src.Settings).Options.Select(o => new SelectOptionEntity { Id = o.Id.ToString(), Description = o.Description }).ToArray()
                        : null));

            CreateMap<SelectOptionEntity, SelectOptionDomain>();

            CreateMap<SelectOptionDomain, SelectOptionEntity>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.ToString()));

            CreateMap<CreativeFieldEntity, CreativeFieldResult>();

            CreateMap<SelectOptionEntity, SelectOptionResult>()
                .ConstructUsing(src => new SelectOptionResult(long.Parse(src.Id), src.Description));

            CreateMap<CreativeFieldType, CreativeFieldTypeEnum>().ConvertUsing(value => value.EnumType);
            CreateMap<CreativeFieldTypeEnum, CreativeFieldType>().ConvertUsing(value => CreativeFieldType.FromValue((int)value));
        }
    }
}
